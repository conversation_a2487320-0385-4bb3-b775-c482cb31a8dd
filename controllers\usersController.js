const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const asyncHandler = require("express-async-handler");
const { generateToken } = require("../configs/genToken");
const User = require("../models/usersModel");
const Session = require("../models/sessionModel");
const { Op } = require('sequelize');


// REGISTER
const registerUser = asyncHandler(async (req, res) => {
  try {
    const { firstName, lastName, username, email, mobile, password } = req.body;

    const existingEmail = await User.findOne({ where: { email } });
    if (existingEmail) {
      return res.status(409).json({ message: "Email is already registered" });
    }
    const existingMobile = await User.findOne({ where: { mobile } });
    if (existingMobile) {
      return res.status(409).json({ message: "Mobile is already registered" });
    }
    const existingUsername = await User.findOne({ where: { username } });
    if (existingUsername) {
      return res.status(409).json({ message: "Username is already taken" });
    }
    const hashedPassword = await bcrypt.hash(password, 16);
    const user = await User.create({
      firstName,
      lastName,
      username,
      email,
      mobile,
      password: hashedPassword,
    });
    const userSafe = {
      fullName: user.firstName + " " + user.lastName,
      username: user.username,
      mobile: user.mobile,
      email: user.email,
    };
    res.status(201).json({ message: "User registered successfully", userInfo: userSafe });
  } catch (error) {
    res.status(500).json({ message: "Registration failed", error: error.message });
  }
});


// LOGIN
const login = asyncHandler(async (req, res) => {
  try {
    const { identifier, password } = req.body;
    const ipWithPort = req.ip || (req.socket && req.socket.remoteAddress) || '';
    const ip = ipWithPort.split(':').pop();
    const user = await User.findOne({
      where: {
        [Op.or]: [
          { email: identifier },
          { mobile: identifier },
          { username: identifier }
        ]
      }
    });

    if (!user) {
      return res.status(401).json({ message: "Invalid Credentials" });
    }
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: "Invalid Credentials" });
    }

const userInfo = {
    id: user.id,
    fullName: user.firstName+" "+user.lastName,
    username: user.username,
    email: user.email,
    mobile: user.mobile,
    role: user.role,
    isVerified: user.isVerified,
    status: user.status,
  };

    const authToken = generateToken({ userInfo });

    await Session.destroy({ where: { userId: user.id, isLoggedIn: true } });

    // Create new session
    await Session.create({
      userId: user.id,
      authToken,
      ipAddress: ip,
      loginTime: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      isLoggedIn: true,
    });

    res.status(200).json({ message: "Login Successfully", authToken });
  } catch (error) {
    res.status(500).json({ message: "Login failed", error: error.message });
  }
});

// LOGOUT
const logout = asyncHandler(async (req, res) => {
  try {
    const authToken = req.authToken;
    if (!authToken) {
      return res.status(401).json({ message: "No token provided" });
    }

    const decoded = jwt.verify(authToken, process.env.JWT_SECRET);

    await Session.update(
      { isLoggedIn: false, logoutTime: new Date() },
      { where: { userId: decoded.id, token, isLoggedIn: true } }
    );

    res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    res.status(500).json({ message: "Logout failed", error: error.message });
  }
});


module.exports = {
  registerUser,
  login,
  logout,
};