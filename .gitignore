# Node modules
/node_modules/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# Environment variables
.env
.env.test
.env.production
.env.local

# Next.js build output
.next

# Nuxt.js build output
.nuxt

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Mac system files
.DS_Store

# Windows system files
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache

# SvelteKit build
.svelte-kit

# Vite build output
dist

# Turbo build cache
.turbo

# pnpm
.pnpm-debug.log