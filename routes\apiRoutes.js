const express = require('express');
const router = express.Router();
const {registerUser, login, logout } = require('../controllers/usersController');
// For Base API Staring Point
router.get('/', (req, res) => {
    res.send('API Is Working!');
});


// ===== Common Routes For All Users =====
router.post('/register',registerUser);
router.post('/login',login);
router.post('/logout',logout);




// Admin Routes All


// Users Routes All



module.exports = router;
