const express = require('express');
const path = require('path');
const { connectDB } = require('./configs/dbCon');  
require('dotenv').config();  
const app = express();
connectDB();
const PORT = process.env.PORT || 4000;

app.set('trust proxy', true);

// ======= Body Parser =======
app.use(express.json());  
app.use(express.urlencoded({ extended: true })); 

app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// ======= Api Routes =======
const apiRoutes = require('./routes/apiRoutes');
app.use('/api', apiRoutes);  

// ======= Server =======
app.listen(PORT, '0.0.0.0', () => {
    console.log(`Api is Running on 🚀 => http://localhost:${PORT}`);
});