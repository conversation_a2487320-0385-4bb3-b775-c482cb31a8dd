const jwt = require("jsonwebtoken");
const Session = require("../models/sessionModel");
const User = require("../models/usersModel");

// AUTHENTICATION
const authMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ message: "No Token Provided" });
    }

    const token = authHeader.split(" ")[1];
    req.token = token;

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const ipWithPort = req.ip || (req.socket && req.socket.remoteAddress) || '';
    const currentIp = ipWithPort.split(':').pop();

    const session = await Session .findOne({
      where: { userId: decoded.id, authToken: token, isLoggedIn: true },
    });

    if (!session) {
      return res.status(401).json({ message: "Session expired or invalid" });
    }

    // Check IP address
    if (session.ipAddress !== currentIp) {
      await Session.update(
        { isLoggedIn: false, logoutTime: new Date() },
        { where: { id: session.id } }
      );
      return res.status(401).json({ message: "Session invalidated due to IP mismatch" });
    }

    // Check expiry
    if (new Date() > session.expiresAt) {
      await Session.update(
        { isLoggedIn: false, logoutTime: new Date() },
        { where: { id: session.id } }
      );
      return res.status(401).json({ message: "Token expired" });
    }

    const user = await User.findByPk(decoded.id);
    if (!user) return res.status(404).json({ message: "User not found" });

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({ message: "Unauthorized", error: error.message });
  }
};

// ROLE & SESSION AUTHORIZATION
const authorizeRoles = (...roles) => {
    return async (req, res, next) => {
        try {
            const currentIp = getClientIp(req);
            const session = await Session.findOne({
                where: { 
                    userId: req.user.id, 
                    authToken: req.token, 
                    isLoggedIn: true,
                    ipAddress: currentIp 
                },
            });

            if (!session) {
                return res.status(401).json({ message: "Session invalid" });
            }

            // Support multiple users: roles can be array of objects { userId, role }
            if (Array.isArray(roles[0]) && typeof roles[0][0] === "object") {
                const allowedUsers = roles[0];
                const match = allowedUsers.find(
                    (u) => u.userId === req.user.id && u.role === req.user.role
                );
                if (!match) {
                    return res.status(403).json({ message: "Access denied" });
                }
                next();
            } else {
                // Default: just check role
                if (!roles.includes(req.user.role)) {
                    return res.status(403).json({ message: "Access denied" });
                }
                next();
            }
        } catch (error) {
            return res.status(500).json({ message: "Authorization error", error: error.message });
        }
    };
};

module.exports = { authMiddleware, authorizeRoles };
