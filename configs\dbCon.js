const { Sequelize } = require("sequelize");
require("dotenv").config();

const Sequelizer = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: process.env.DB_DIALECT,
    port: process.env.DB_PORT || 3306,
    logging: false,
    pool: { max: 10, min: 0, acquire: 30000, idle: 10000 },
  }
);

const connectDB = async () => {
  try {
    await Sequelizer.authenticate();
    console.log("DB Connected ✅ To MariaDB/MySQL Via Mysql2");
    if(process.env.SERVER_TYPE === "PRODUCTION"){
      await Sequelizer.sync({ alter: false }); 
    }else if ( process.env.SERVER_TYPE === "STAGING" ){
      // await Sequelizer.sync({ force: true });
      // console.log("DB Synced ✅");
    }
    await Sequelizer.sync({ alter: true }); 
  } catch (error) {
    console.error("DB Connection ❌ Failed !...", error.message);
    process.exit(1);
  }
};

module.exports = { connectDB, Sequelizer };
